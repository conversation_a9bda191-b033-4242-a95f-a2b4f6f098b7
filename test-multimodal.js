#!/usr/bin/env node

/**
 * Test script for Vertex AI multimodal embeddings
 * 
 * This script demonstrates how to:
 * 1. Upload image files
 * 2. Upload documents with images
 * 3. Generate test cases from multimodal content
 * 
 * Usage:
 *   node test-multimodal.js
 * 
 * Prerequisites:
 *   - Set LLM=VERTEX in .env
 *   - Have valid Google Cloud credentials
 *   - Service running on localhost:3000
 */

const fs = require('fs');
const path = require('path');
const FormData = require('form-data');
const fetch = require('node-fetch');

const API_BASE = 'http://localhost:3000';
const JWT_TOKEN = 'your-jwt-token-here'; // Replace with actual JWT token

async function uploadFile(filePath, description) {
  console.log(`\n📁 Uploading: ${description}`);
  console.log(`   File: ${filePath}`);
  
  if (!fs.existsSync(filePath)) {
    console.log(`   ❌ File not found: ${filePath}`);
    return null;
  }

  const form = new FormData();
  form.append('file[]', fs.createReadStream(filePath));

  try {
    const response = await fetch(`${API_BASE}/file-upload`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${JWT_TOKEN}`,
        ...form.getHeaders()
      },
      body: form
    });

    if (!response.ok) {
      const error = await response.text();
      console.log(`   ❌ Upload failed: ${response.status} ${error}`);
      return null;
    }

    const result = await response.json();
    console.log(`   ✅ Uploaded successfully`);
    console.log(`   📄 File ID: ${result[0].id}`);
    console.log(`   🔗 URL: ${result[0].url}`);
    console.log(`   📊 Size: ${result[0].size} bytes`);
    
    return result[0];
  } catch (error) {
    console.log(`   ❌ Error: ${error.message}`);
    return null;
  }
}

async function generateTestCases(fileId, description) {
  console.log(`\n🧪 Generating test cases for: ${description}`);
  
  try {
    const response = await fetch(`${API_BASE}/file-upload/${fileId}/test-cases`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${JWT_TOKEN}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      const error = await response.text();
      console.log(`   ❌ Test case generation failed: ${response.status} ${error}`);
      return null;
    }

    const result = await response.json();
    console.log(`   ✅ Generated ${result.testCases?.length || 0} test cases`);
    
    if (result.testCases && result.testCases.length > 0) {
      console.log(`   📋 Sample test case: "${result.testCases[0].name}"`);
    }
    
    return result;
  } catch (error) {
    console.log(`   ❌ Error: ${error.message}`);
    return null;
  }
}

async function checkServiceStatus() {
  console.log('🔍 Checking service status...');
  
  try {
    const response = await fetch(`${API_BASE}/health`);
    if (response.ok) {
      console.log('✅ Service is running');
      return true;
    } else {
      console.log('❌ Service is not responding correctly');
      return false;
    }
  } catch (error) {
    console.log('❌ Service is not running or not accessible');
    console.log('   Make sure the service is started with: npm run start:dev');
    return false;
  }
}

async function main() {
  console.log('🚀 Testing Vertex AI Multimodal Embeddings');
  console.log('==========================================');

  // Check if service is running
  const serviceRunning = await checkServiceStatus();
  if (!serviceRunning) {
    process.exit(1);
  }

  // Test files to upload (create these or modify paths as needed)
  const testFiles = [
    {
      path: './test-files/sample-image.png',
      description: 'Sample PNG image',
      type: 'image'
    },
    {
      path: './test-files/chart.jpg', 
      description: 'Chart/diagram image',
      type: 'image'
    },
    {
      path: './test-files/document-with-images.pdf',
      description: 'PDF with embedded images',
      type: 'document'
    },
    {
      path: './test-files/flowchart.png',
      description: 'Process flowchart',
      type: 'image'
    }
  ];

  console.log('\n📋 Test Plan:');
  console.log('1. Upload various file types with images');
  console.log('2. Generate embeddings using Vertex AI multimodal model');
  console.log('3. Generate test cases from multimodal content');
  console.log('4. Compare results with text-only processing');

  const uploadedFiles = [];

  // Upload test files
  for (const testFile of testFiles) {
    const result = await uploadFile(testFile.path, testFile.description);
    if (result) {
      uploadedFiles.push({
        ...result,
        description: testFile.description,
        type: testFile.type
      });
    }
  }

  if (uploadedFiles.length === 0) {
    console.log('\n❌ No files were uploaded successfully');
    console.log('   Create test files in ./test-files/ directory or modify file paths');
    process.exit(1);
  }

  // Wait for embeddings to be processed
  console.log('\n⏳ Waiting for embeddings to be processed...');
  await new Promise(resolve => setTimeout(resolve, 5000));

  // Generate test cases for uploaded files
  for (const file of uploadedFiles) {
    await generateTestCases(file.id, file.description);
  }

  console.log('\n🎉 Test completed!');
  console.log('\n📊 Summary:');
  console.log(`   📁 Files uploaded: ${uploadedFiles.length}`);
  console.log(`   🖼️  Image files: ${uploadedFiles.filter(f => f.type === 'image').length}`);
  console.log(`   📄 Document files: ${uploadedFiles.filter(f => f.type === 'document').length}`);

  console.log('\n💡 Next steps:');
  console.log('   1. Check the database for generated embeddings');
  console.log('   2. Compare embedding dimensions with text-only embeddings');
  console.log('   3. Review generated test cases for visual content understanding');
  console.log('   4. Test search functionality with multimodal queries');

  console.log('\n🔧 Configuration check:');
  console.log('   - Ensure LLM=VERTEX in your .env file');
  console.log('   - Verify Google Cloud credentials are correct');
  console.log('   - Check that Vertex AI API is enabled in your GCP project');
}

// Handle errors gracefully
process.on('unhandledRejection', (error) => {
  console.error('\n❌ Unhandled error:', error.message);
  process.exit(1);
});

// Run the test
main().catch(error => {
  console.error('\n❌ Test failed:', error.message);
  process.exit(1);
});
