{"name": "backend_ai", "version": "0.0.1", "description": "NestJS AI backend service", "private": true, "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "start:worker": "node dist/worker", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix"}, "dependencies": {"@bull-board/api": "^6.7.10", "@bull-board/express": "^6.7.10", "@bull-board/nestjs": "^6.7.10", "@google-cloud/aiplatform": "^5.5.0", "@google-cloud/storage": "^7.7.0", "@google/generative-ai": "^0.24.0", "@modelcontextprotocol/sdk": "^1.17.1", "@nestjs/bullmq": "^11.0.2", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.1.1", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^10.0.0", "@nestjs/swagger": "^7.3.0", "@nestjs/typeorm": "^10.0.0", "@types/js-yaml": "^4.0.9", "atlassian-jwt": "^2.0.3", "bullmq": "^5.41.8", "cheerio": "^1.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "google-auth-library": "^10.2.1", "ioredis": "^5.3.2", "js-yaml": "^4.1.0", "mammoth": "^1.6.0", "multer": "^1.4.5-lts.1", "openai": "^4.28.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "pdf-parse": "^1.1.1", "pg": "^8.11.3", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "typeorm": "^0.3.17", "uuid": "^9.0.1"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@types/express": "^4.17.17", "@types/multer": "^1.4.11", "@types/node": "^20.3.1", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}}