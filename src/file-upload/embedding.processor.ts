import { OnWorkerEvent, Processor, WorkerHost } from '@nestjs/bullmq';
import { Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Embedding } from './embedding.entity';
import { LLMService } from './openai.service';
import { FileProcessorService } from './file-processor.service';
import { ApiDocumentationDetectorService } from '../publish-url/api-documentation-detector.service';
import { ApiDocumentationParserService } from '../publish-url/api-documentation-parser.service';
import { ApiDocumentationMcpService } from '../mcp/api-documentation-mcp.service';
import * as cheerio from 'cheerio';

interface EmbeddingJob {
  fileId: string;
  fileBuffer: Buffer;
  mimetype: string;
  url?: string;
  isPublishUrl?: boolean;
  integration?: string;
  integration_email?: string;
  integration_api_token?: string;
  isApiDocumentation?: boolean;
  apiDetectionInfo?: any;
  extractedContent?: ExtractedContent;
}

export interface ExtractedContent {
  text: string;
  images?: Array<{
    buffer: Buffer;
    mimetype: string;
    description?: string;
  }>;
  hasImages: boolean;
}

@Processor('embeddings')
export class EmbeddingProcessor extends WorkerHost {
  private readonly logger = new Logger(EmbeddingProcessor.name);

  constructor(
    @InjectRepository(Embedding)
    private embeddingRepository: Repository<Embedding>,
    private openAIService: LLMService,
    private fileProcessorService: FileProcessorService,
    private apiDocumentationDetector: ApiDocumentationDetectorService,
    private apiDocumentationParser: ApiDocumentationParserService,
    private mcpService: ApiDocumentationMcpService,
  ) {
    super();
  }

  private async processUrlContent(url: string, integration?: string, credentials?: { email: string; token: string }, isApiDocumentation?: boolean, apiDetectionInfo?: any): Promise<string> {
    try {
      if (integration) {
        // Handle private URL (e.g., Confluence)
        return await this.fetchPrivateUrlContent(url, integration, credentials);
      }

      // Handle API documentation
      if (isApiDocumentation && apiDetectionInfo) {
        return await this.processApiDocumentationContent(url, apiDetectionInfo);
      }

      // Determine content type before fetching full content
      const headResponse = await fetch(url, { method: 'HEAD' });
      const contentType = headResponse.headers.get('content-type') || '';

      // Skip non-text content types
      if (!contentType.includes('text/html') &&
          !contentType.includes('text/plain') &&
          !contentType.includes('application/json')) {
        this.logger.warn(`Skipping non-text URL: ${url} (${contentType})`);
        return '';
      }

      // Handle public URL
      const response = await fetch(url);
      const html = await response.text();

      return this.cleanHtmlContent(html);
    } catch (error) {
      this.logger.error(`Error processing URL ${url}:`, error);
      throw new Error(`Failed to process URL: ${error.message}`);
    }
  }

  private async processApiDocumentationContent(url: string, apiDetectionInfo: any): Promise<string> {
    try {
      this.logger.log(`Processing API documentation: ${url} (Type: ${apiDetectionInfo.type})`);

      // Parse the API documentation and store in MCP
      const parsedApi = await this.apiDocumentationParser.parseApiDocumentation(url, apiDetectionInfo);

      // Generate API ID for MCP storage
      const apiId = this.mcpService.generateApiId(url);

      // Verify that the API documentation was stored in MCP
      const hasApiData = await this.mcpService.hasApiDocumentation(apiId);
      if (hasApiData) {
        this.logger.log(`API documentation successfully stored in MCP with ID: ${apiId}`);

        // For MCP-based approach, we return a minimal content summary
        // The actual detailed API information will be accessed via MCP tools during test generation
        const content = `API Documentation Summary:
Title: ${parsedApi.title}
Description: ${parsedApi.description || 'No description'}
Version: ${parsedApi.version || 'Unknown'}
Base URL: ${parsedApi.baseUrl || 'Not specified'}
Total Endpoints: ${parsedApi.endpoints.length}
Total Schemas: ${Object.keys(parsedApi.schemas || {}).length}
MCP API ID: ${apiId}

This API documentation has been processed and stored in the MCP (Model Context Protocol) system for intelligent access during test case generation. The MCP system provides structured access to all endpoints, schemas, authentication methods, and other API details.

Key Features:
- Structured endpoint access via MCP tools
- Intelligent test case generation based on API structure
- Comprehensive coverage of all API operations
- Dynamic querying of API documentation components

The test generation system will use MCP tools to intelligently query the API structure and generate comprehensive test cases covering all endpoints and scenarios.`;

        this.logger.log(`Generated MCP-based content summary for ${parsedApi.endpoints.length} endpoints`);
        return content;
      } else {
        this.logger.warn(`Failed to store API documentation in MCP, falling back to content-based approach`);
        // Fallback to the original content-based approach
        return this.generateContentBasedApiSummary(parsedApi);
      }
    } catch (error) {
      this.logger.error(`Error processing API documentation ${url}:`, error);
      // Fallback to regular HTML processing
      const response = await fetch(url);
      const html = await response.text();
      return this.cleanHtmlContent(html);
    }
  }

  private generateContentBasedApiSummary(parsedApi: any): string {
    // Fallback method that generates content-based summary (original approach)
    let content = `API Documentation: ${parsedApi.title}\n\n`;

    if (parsedApi.description) {
      content += `Description: ${parsedApi.description}\n\n`;
    }

    if (parsedApi.version) {
      content += `Version: ${parsedApi.version}\n\n`;
    }

    if (parsedApi.baseUrl) {
      content += `Base URL: ${parsedApi.baseUrl}\n\n`;
    }

    // Add endpoint information (limited to prevent content overload)
    const endpointsToShow = parsedApi.endpoints.slice(0, 20); // Limit to first 20 endpoints
    content += `API Endpoints (showing ${endpointsToShow.length} of ${parsedApi.endpoints.length} total):\n\n`;

    for (const endpoint of endpointsToShow) {
      content += `${endpoint.method} ${endpoint.path}\n`;

      if (endpoint.summary) {
        content += `Summary: ${endpoint.summary}\n`;
      }

      if (endpoint.tags && endpoint.tags.length > 0) {
        content += `Tags: ${endpoint.tags.join(', ')}\n`;
      }

      content += '\n';
    }

    if (parsedApi.endpoints.length > 20) {
      content += `... and ${parsedApi.endpoints.length - 20} more endpoints\n\n`;
    }

    // Add schema information if available (limited)
    if (parsedApi.schemas && Object.keys(parsedApi.schemas).length > 0) {
      const schemaNames = Object.keys(parsedApi.schemas).slice(0, 10);
      content += `\nAPI Schemas (showing ${schemaNames.length} of ${Object.keys(parsedApi.schemas).length} total):\n`;
      content += schemaNames.join(', ') + '\n';

      if (Object.keys(parsedApi.schemas).length > 10) {
        content += `... and ${Object.keys(parsedApi.schemas).length - 10} more schemas\n`;
      }
    }

    this.logger.log(`Generated content-based summary for ${parsedApi.endpoints.length} endpoints`);
    return content;
  }

  private async cleanHtmlContent(html: string): Promise<string> {
    const cheerio = await import('cheerio');
    const $ = cheerio.load(html);
    
    // Remove common non-relevant elements
    $('script, style, noscript, iframe, video, audio, svg, canvas, footer, nav, header, aside').remove();
    $('[class*="advert"], [class*="banner"], [class*="promo"], [id*="ad-"], [class*="social"]').remove();
    $('[class*="cookie"], [class*="popup"], [class*="modal"], [class*="overlay"]').remove();
    $('[class*="animation"], [class*="slider"], [class*="carousel"]').remove();
    
    // Extract the main content
    const contentSelectors = [
      'article', 'main', '.content', '#content', '.post', '.entry', 
      '.documentation', '.docs', '.main-content', '[role="main"]'
    ];
    
    let mainContent = '';
    
    // Try to find main content container
    for (const selector of contentSelectors) {
      if ($(selector).length) {
        mainContent = $(selector).text();
        break;
      }
    }
    
    // If no main content found, extract body text with fallback
    if (!mainContent.trim()) {
      mainContent = $('body').text();
    }
    
    // Clean up the text
    let cleanedText = mainContent
      .replace(/\s+/g, ' ')
      .replace(/\n+/g, '\n')
      .trim();
      
    // Handle empty or very short content
    if (cleanedText.length < 100) {
      this.logger.warn('Content too short after cleaning');
      const title = $('title').text();
      const description = $('meta[name="description"]').attr('content') || '';
      cleanedText = `${title}\n\n${description}`;
    }
    
    return cleanedText;
  }
  
  private async fetchPrivateUrlContent(url: string, integration: string, credentials?: { email: string; token: string }): Promise<string> {
    try {
      let content = '';

      switch (integration.toLowerCase()) {
        case 'atlassian': {
          if (!credentials?.email || !credentials?.token) {
            throw new Error('Atlassian email and API token are required');
          }

          // Determine if it's a Confluence or JIRA URL
          const isConfluence = url.includes('/wiki/') || url.includes('/pages/') || url.includes('/display/');
          const isJira = url.includes('/browse/');

          if (isConfluence) {
            // Extract page ID from Confluence URL
            const pageIdMatch = url.match(/pages\/(\d+)/);
            if (!pageIdMatch) {
              throw new Error('Invalid Confluence URL format');
            }
            const pageId = pageIdMatch[1];

            // Extract base URL (e.g., https://your-domain.atlassian.net/wiki)
            const baseUrlMatch = url.match(/^(https?:\/\/[^\/]+(?:\/wiki|\/display)?)\/?/);
            let baseUrl = baseUrlMatch ? baseUrlMatch[1] : '';
            const normalizedBaseUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;

            const apiUrl = `${normalizedBaseUrl}/rest/api/content/${pageId}?expand=body.storage`;
            this.logger.debug(`Fetching Confluence content for page ID: ${pageId} from URL: ${apiUrl}`);

            const response = await fetch(apiUrl, {
              headers: {
                'Authorization': `Basic ${Buffer.from(`${credentials.email}:${credentials.token}`).toString('base64')}`,
                'Accept': 'application/json',
              },
            });

            if (!response.ok) {
              let errorMessage = `Confluence API error: ${response.status} ${response.statusText}`;
              if (response.status === 401) {
                errorMessage = 'Confluence API error: Unauthorized. Check your credentials.';
              } else if (response.status === 404) {
                errorMessage = 'Confluence API error: Page not found.';
              } else if (response.status === 500) {
                errorMessage = 'Confluence API error: Internal server error.';
              }
              this.logger.error(errorMessage);
              throw new Error(errorMessage);
            }

            const data = await response.json();
            
            if (!data.body?.storage?.value) {
              throw new Error('No content found in Confluence response');
            }

            // Convert Confluence storage format (HTML) to plain text
            const $ = cheerio.load(data.body.storage.value);
            content = $.text();

            // Clean up the content
            content = content
              .replace(/\s+/g, ' ')
              .replace(/\n+/g, '\n')
              .trim();

            this.logger.debug(`Successfully fetched Confluence content for page ${pageId}`);
          } else if (isJira) {
            // Extract issue key from JIRA URL
            const issueKeyMatch = url.match(/\/browse\/([A-Z]+-\d+)/);
            if (!issueKeyMatch) {
              throw new Error('Invalid JIRA URL format');
            }
            const issueKey = issueKeyMatch[1];

            // Extract base URL (e.g., https://your-domain.atlassian.net)
            const baseUrlMatch = url.match(/^(https?:\/\/[^\/]+)/);
            if (!baseUrlMatch) {
              throw new Error('Invalid JIRA URL format');
            }
            const baseUrl = baseUrlMatch[1];

            // Fetch issue details using JIRA REST API
            const apiUrl = `${baseUrl}/rest/api/2/issue/${issueKey}?fields=summary,description,attachment,customfield_10000`;
            this.logger.debug(`Fetching JIRA content for issue: ${issueKey} from URL: ${apiUrl}`);

            const response = await fetch(apiUrl, {
              headers: {
                'Authorization': `Basic ${Buffer.from(`${credentials.email}:${credentials.token}`).toString('base64')}`,
                'Accept': 'application/json',
              },
            });

            if (!response.ok) {
              let errorMessage = `JIRA API error: ${response.status} ${response.statusText}`;
              if (response.status === 401) {
                errorMessage = 'JIRA API error: Unauthorized. Check your credentials.';
              } else if (response.status === 404) {
                errorMessage = 'JIRA API error: Issue not found.';
              }
              this.logger.error(errorMessage);
              throw new Error(errorMessage);
            }

            const data = await response.json();
            
            // Extract relevant fields
            const summary = data.fields.summary || '';
            const description = data.fields.description || '';
            
            // Try to get acceptance criteria (often stored in customfield_10000 as "Acceptance Criteria")
            // Note: The actual field ID may vary between JIRA instances
            let acceptanceCriteria = '';
            if (data.fields.customfield_10000) {
              acceptanceCriteria = data.fields.customfield_10000;
            }
            
            // Combine the content
            content = `JIRA Issue: ${issueKey}\n\nSummary: ${summary}\n\nDescription: ${description}\n\nAcceptance Criteria: ${acceptanceCriteria}`;
            
            // Handle attachments if available
            if (data.fields.attachment && data.fields.attachment.length > 0) {
              // Get the first few attachments (limit to avoid overloading)
              const attachmentsToProcess = data.fields.attachment.slice(0, 3);
              
              for (const attachment of attachmentsToProcess) {
                if (this.isTextBasedAttachment(attachment.filename)) {
                  try {
                    // Download attachment content
                    const attachmentResponse = await fetch(attachment.content, {
                      headers: {
                        'Authorization': `Basic ${Buffer.from(`${credentials.email}:${credentials.token}`).toString('base64')}`,
                      },
                    });
                    
                    if (attachmentResponse.ok) {
                      const attachmentContent = await attachmentResponse.text();
                      content += `\n\nAttachment (${attachment.filename}):\n${attachmentContent}`;
                    }
                  } catch (attachmentError) {
                    this.logger.warn(`Failed to fetch attachment ${attachment.filename}: ${attachmentError.message}`);
                  }
                }
              }
            }
            
            this.logger.debug(`Successfully fetched JIRA content for issue ${issueKey}`);
          } else {
            throw new Error('Unsupported Atlassian URL format. Must be a Confluence or JIRA URL.');
          }
          break;
        }

        case 'sharepoint':
          // Implement SharePoint API call here
          this.logger.debug(`Fetching content from SharePoint URL: ${url}`);
          throw new Error('SharePoint integration not implemented yet');

        default:
          throw new Error(`Unsupported integration: ${integration}`);
      }

      if (!content) {
        throw new Error(`Failed to fetch content from private URL: ${url}`);
      }

      return content;
    } catch (error) {
      this.logger.error(`Error fetching private URL content: ${url}`, error);
      throw error;
    }
  }

  // Helper method to determine if an attachment is text-based
  private isTextBasedAttachment(filename: string): boolean {
    const textExtensions = ['.txt', '.md', '.json', '.csv', '.xml', '.html', '.js', '.ts', '.py', '.java', '.c', '.cpp', '.h', '.cs'];
    const extension = filename.substring(filename.lastIndexOf('.')).toLowerCase();
    return textExtensions.includes(extension);
  }

  async process(job: Job<EmbeddingJob>): Promise<void> {
    this.logger.debug(`Processing embedding job ${job.id}`);

    try {
      let text: string;

      // Extract text based on job type (file or URL)
      if (job.data.fileBuffer && job.data.mimetype) {
        // Process file - check if we should use multimodal processing
        const file = {
          buffer: Buffer.from(job.data.fileBuffer),
          mimetype: job.data.mimetype,
          originalname: 'uploaded-file'
        } as Express.Multer.File;

        // Use multimodal processing for supported file types when using Vertex AI
        if (this.fileProcessorService.supportsMultimodal(job.data.mimetype)) {
          this.logger.log(`File ${job.data.mimetype} supports multimodal processing`);
          const extractedContent = await this.fileProcessorService.extractContent(file);
          text = extractedContent.text;

          this.logger.log(`Extracted content - hasImages: ${extractedContent.hasImages}, images: ${extractedContent.images?.length || 0}`);

          // Store extracted content for potential multimodal embedding
          job.data.extractedContent = extractedContent;
        } else {
          this.logger.log(`File ${job.data.mimetype} does not support multimodal processing`);
          text = await this.fileProcessorService.extractText(file);
        }
      } else if (job.data.url) {
        // Fetch and process URL content
        text = await this.processUrlContent(
          job.data.url,
          job.data.integration,
          job.data.integration_email && job.data.integration_api_token
            ? {
                email: job.data.integration_email,
                token: job.data.integration_api_token,
              }
            : undefined,
          job.data.isApiDocumentation,
          job.data.apiDetectionInfo
        );
      } else {
        throw new Error('Invalid job data: requires either file or URL');
      }

      // Split text into chunks
      const chunks = this.fileProcessorService.splitIntoChunks(text);

      // Process chunks in batches to avoid rate limits
      const batchSize = 20;
      for (let i = 0; i < chunks.length; i += batchSize) {
        const batchChunks = chunks.slice(i, i + batchSize);
        
        // Create the embedding entities with correct ID field
        const embeddingEntities = batchChunks.map(content => ({
          content,
          embedding: [], // Will be updated after OpenAI call
          ...(job.data.isPublishUrl 
            ? { publishUrlId: job.data.fileId }
            : { fileUploadId: job.data.fileId }
          ),
        }));

        // Save the embedding entities first to get their IDs
        const savedEmbeddings = await this.embeddingRepository.save(embeddingEntities);

        // Create embeddings for the batch using all embedding IDs for token tracking
        let embeddings: number[][];

        // Check if we should use multimodal embeddings
        const extractedContent = (job.data as any).extractedContent;
        this.logger.log(`Checking for multimodal content - extractedContent exists: ${!!extractedContent}`);

        if (extractedContent) {
          this.logger.log(`ExtractedContent details - hasImages: ${extractedContent.hasImages}, images count: ${extractedContent.images?.length || 0}`);
        }

        if (extractedContent && extractedContent.hasImages && extractedContent.images && extractedContent.images.length > 0) {
          // Use multimodal embeddings for content with images
          this.logger.log('🖼️ Creating multimodal embeddings for content with images');
          embeddings = [];

          for (let j = 0; j < batchChunks.length; j++) {
            const chunk = batchChunks[j];
            const embeddingId = savedEmbeddings[j].id;

            // Use the first image for multimodal embedding
            const firstImage = extractedContent.images[0];
            const embedding = await this.openAIService.createMultimodalEmbedding(
              chunk,
              firstImage.buffer,
              firstImage.mimetype,
              embeddingId
            );
            embeddings.push(embedding);
          }
        } else {
          // Use regular text embeddings
          embeddings = await this.openAIService.createEmbeddings(
            batchChunks,
            savedEmbeddings.map(e => e.id)
          );
        }

        // Update the embeddings with their vector data
        await Promise.all(
          savedEmbeddings.map((entity, index) =>
            this.embeddingRepository.update(entity.id, {
              embedding: embeddings[index]
            })
          )
        );

        // Update job progress
        await job.updateProgress((i + batchSize) / chunks.length * 100);

        // Add a small delay between batches to respect rate limits
        if (i + batchSize < chunks.length) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      this.logger.debug(`Completed embedding job ${job.id}`);
    } catch (error) {
      this.logger.error(`Error processing embedding job ${job.id}:`, error);
      throw error;
    }
  }

  @OnWorkerEvent('completed')
  onCompleted(job: Job) {
    this.logger.debug(`Job ${job.id} completed successfully`);
  }

  @OnWorkerEvent('failed')
  onFailed(job: Job, error: Error) {
    this.logger.error(`Job ${job.id} failed:`, error);
  }
}
