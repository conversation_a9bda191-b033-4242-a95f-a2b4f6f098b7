import { Injectable } from '@nestjs/common';
import * as pdfjsLib from 'pdf-parse';
import * as mammoth from 'mammoth';

export interface ExtractedContent {
  text: string;
  images?: Array<{
    buffer: Buffer;
    mimetype: string;
    description?: string;
  }>;
  hasImages: boolean;
}

@Injectable()
export class FileProcessorService {
  async extractText(file: Express.Multer.File): Promise<string> {
    switch (file.mimetype) {
      case 'application/pdf':
        return this.extractFromPDF(file.buffer);
      case 'application/msword':
      case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
        return this.extractFromDOCX(file.buffer);
      case 'text/plain':
      case 'text/markdown':
      case 'text/html':
      case 'text/csv':
        return this.extractFromText(file.buffer);
      case 'image/jpeg':
      case 'image/png':
      case 'image/gif':
      case 'image/webp':
        return this.extractFromImage(file.buffer, file.mimetype);
      default:
        throw new Error(`Unsupported file type: ${file.mimetype}`);
    }
  }

  /**
   * Enhanced extraction that returns both text and images
   * This enables multimodal processing with Vertex AI
   */
  async extractContent(file: Express.Multer.File): Promise<ExtractedContent> {
    switch (file.mimetype) {
      case 'application/pdf':
        return this.extractContentFromPDF(file.buffer);
      case 'application/msword':
      case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
        return this.extractContentFromDOCX(file.buffer);
      case 'text/plain':
      case 'text/markdown':
      case 'text/html':
      case 'text/csv':
        return {
          text: await this.extractFromText(file.buffer),
          hasImages: false
        };
      case 'image/jpeg':
      case 'image/png':
      case 'image/gif':
      case 'image/webp':
        return {
          text: `[Image file: ${file.originalname}]`,
          images: [{
            buffer: file.buffer,
            mimetype: file.mimetype,
            description: `Image file: ${file.originalname}`
          }],
          hasImages: true
        };
      default:
        throw new Error(`Unsupported file type: ${file.mimetype}`);
    }
  }

  private async extractFromPDF(buffer: Buffer): Promise<string> {
    const data = await pdfjsLib(buffer);
    return data.text;
  }

  private async extractFromDOCX(buffer: Buffer): Promise<string> {
    const result = await mammoth.extractRawText({ buffer });
    return result.value;
  }

  private async extractFromText(buffer: Buffer): Promise<string> {
    return buffer.toString('utf-8');
  }

  private async extractFromImage(buffer: Buffer, mimetype: string): Promise<string> {
    // For image files, return a placeholder text
    // The actual image processing will be handled by multimodal embeddings
    return `[Image content - ${mimetype}]`;
  }

  /**
   * Enhanced PDF extraction that can identify and extract images
   */
  private async extractContentFromPDF(buffer: Buffer): Promise<ExtractedContent> {
    try {
      const data = await pdfjsLib(buffer);

      // Basic implementation - just extract text for now
      // In a full implementation, you could use libraries like pdf2pic or pdf-poppler
      // to extract images from PDFs
      return {
        text: data.text,
        hasImages: false // TODO: Implement image extraction from PDF
      };
    } catch (error) {
      throw new Error(`Failed to extract content from PDF: ${error.message}`);
    }
  }

  /**
   * Enhanced DOCX extraction that can identify and extract images
   */
  private async extractContentFromDOCX(buffer: Buffer): Promise<ExtractedContent> {
    try {
      const result = await mammoth.extractRawText({ buffer });

      // Basic implementation - just extract text for now
      // Mammoth can extract images, but it requires additional configuration
      return {
        text: result.value,
        hasImages: false // TODO: Implement image extraction from DOCX
      };
    } catch (error) {
      throw new Error(`Failed to extract content from DOCX: ${error.message}`);
    }
  }

  splitIntoChunks(text: string, maxChunkSize: number = 1000): string[] {
    const sentences = text.match(/[^.!?]+[.!?]+/g) || [];
    const chunks: string[] = [];
    let currentChunk = '';

    for (const sentence of sentences) {
      if ((currentChunk + sentence).length <= maxChunkSize) {
        currentChunk += sentence;
      } else {
        if (currentChunk) chunks.push(currentChunk.trim());
        currentChunk = sentence;
      }
    }

    if (currentChunk) chunks.push(currentChunk.trim());
    return chunks;
  }

  /**
   * Check if a file type supports multimodal processing
   */
  supportsMultimodal(mimetype: string): boolean {
    const multimodalTypes = [
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'application/pdf', // PDFs can contain images
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document' // DOCX can contain images
    ];

    return multimodalTypes.includes(mimetype);
  }
}