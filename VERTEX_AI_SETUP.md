# Vertex AI Multimodal Embeddings Setup

## Overview

This service now supports Vertex AI's `multimodalembedding@001` model, which can process both text and images to create embeddings. This enables the service to handle files containing images and diagrams.

## Configuration

### Environment Variables

Add the following to your `.env` file:

```bash
# Set LLM provider to VERTEX to enable multimodal embeddings
LLM=VERTEX

# Google Cloud credentials (already configured)
GOOGLE_CLOUD_PROJECT_ID=your-project-id
GOOGLE_CLOUD_CLIENT_EMAIL=<EMAIL>
GOOGLE_CLOUD_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\n"
```

### Supported File Types

With Vertex AI enabled, the service now supports:

**Text-only files:**
- `.doc`, `.docx` - Microsoft Word documents
- `.pdf` - PDF documents
- `.txt` - Plain text files
- `.md` - Markdown files
- `.html` - HTML files
- `.csv` - CSV files
- `.xls`, `.xlsx` - Excel files

**Image files (new with Vertex AI):**
- `.jpg`, `.jpeg` - JPEG images
- `.png` - PNG images
- `.gif` - GIF images
- `.webp` - WebP images

**Multimodal files:**
- `.pdf` - PDFs with embedded images (future enhancement)
- `.docx` - Word documents with embedded images (future enhancement)

## How It Works

### 1. Text-Only Processing (OpenAI/Gemini)
```
File → Extract Text → Create Text Embedding → Store
```

### 2. Multimodal Processing (Vertex AI)
```
File → Extract Text + Images → Create Multimodal Embedding → Store
```

### 3. Image-Only Processing (Vertex AI)
```
Image File → Create Multimodal Embedding (with placeholder text) → Store
```

## API Usage

### Upload Files with Images

```bash
curl -X POST http://localhost:3000/file-upload \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -F "file[]=@document_with_diagrams.pdf" \
  -F "file[]=@chart.png" \
  -F "file[]=@flowchart.jpg"
```

### Response
```json
[
  {
    "id": "uuid-1",
    "filename": "document_with_diagrams.pdf",
    "mimetype": "application/pdf",
    "url": "https://storage.googleapis.com/bucket/file.pdf",
    "size": 1024000,
    "createdAt": "2024-02-20T12:00:00Z"
  },
  {
    "id": "uuid-2", 
    "filename": "chart.png",
    "mimetype": "image/png",
    "url": "https://storage.googleapis.com/bucket/chart.png",
    "size": 256000,
    "createdAt": "2024-02-20T12:00:00Z"
  }
]
```

## Benefits

### 1. Enhanced Content Understanding
- Can process documents with charts, diagrams, and images
- Better context understanding for technical documentation
- Improved test case generation for visual content

### 2. Broader File Support
- Direct image file uploads
- Future support for extracting images from PDFs and Word documents

### 3. Better Test Case Generation
- Generate test cases that consider visual elements
- More comprehensive coverage for applications with UI components
- Better understanding of workflow diagrams and system architectures

## Technical Implementation

### New Methods in LLMService

```typescript
// Create multimodal embedding with text and image
async createMultimodalEmbedding(
  text: string, 
  imageBuffer?: Buffer, 
  imageMimeType?: string,
  embeddingId?: string
): Promise<number[]>

// Check if provider supports multimodal
supportsMultimodal(): boolean
```

### Enhanced FileProcessorService

```typescript
// Extract both text and images
async extractContent(file: Express.Multer.File): Promise<ExtractedContent>

// Check if file type supports multimodal processing
supportsMultimodal(mimetype: string): boolean
```

## Migration Guide

### From OpenAI/Gemini to Vertex AI

1. **Update Environment Variable:**
   ```bash
   # Change from
   LLM=OPENAI  # or GEMINI
   
   # To
   LLM=VERTEX
   ```

2. **Verify Google Cloud Credentials:**
   - Ensure your service account has Vertex AI permissions
   - Verify the project ID is correct

3. **Test with Image Files:**
   ```bash
   # Upload an image file to test multimodal embeddings
   curl -X POST http://localhost:3000/file-upload \
     -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     -F "file[]=@test-image.png"
   ```

### Backward Compatibility

- All existing text-only functionality remains unchanged
- Existing embeddings continue to work
- Can switch back to OpenAI/Gemini by changing the `LLM` environment variable

## Troubleshooting

### Common Issues

1. **"Multimodal embeddings are only supported with Vertex AI provider"**
   - Solution: Set `LLM=VERTEX` in your environment

2. **"No embedding returned from Vertex AI"**
   - Check Google Cloud credentials
   - Verify project has Vertex AI API enabled
   - Ensure service account has proper permissions

3. **"Unsupported file type"**
   - Check if the file type is in the ALLOWED_MIME_TYPES list
   - Verify the file is not corrupted

### Logs

Monitor the application logs for:
- `Creating multimodal embeddings for content with images`
- `Vertex AI embedding error: ...`
- `Generated X test cases for multimodal content`

## Future Enhancements

1. **PDF Image Extraction:** Extract images from PDF files for multimodal processing
2. **DOCX Image Extraction:** Extract images from Word documents
3. **OCR Integration:** Extract text from images using OCR
4. **Video Support:** Process video files for multimodal embeddings
5. **Audio Support:** Process audio files with speech-to-text
